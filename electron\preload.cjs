const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron')

contextBridge.exposeInMainWorld('electronAPI', {
  // 窗口控制
  minimizeWindow: () => ipcRenderer.invoke('minimize-window'),
  closeWindow: () => ipcRenderer.invoke('close-window'),
  showWindow: () => ipcRenderer.invoke('show-window'),
  minimizeToDesktop: () => ipcRenderer.invoke('minimize-to-desktop'),
  restoreMainWindow: () => ipcRenderer.invoke('restore-main-window'),

  // 窗口拖动 (注意：实际的拖动通过CSS -webkit-app-region实现)
  moveWindow: (deltaX, deltaY) => ipcRenderer.invoke('move-window', deltaX, deltaY),

  // 文件上传
  uploadAudio: (audioBlob) => ipcRenderer.invoke('upload-audio', audioBlob),

  // 获取URL参数来判断是否为小窗口模式
  getUrlParams: () => new URLSearchParams(window.location.search),
})
