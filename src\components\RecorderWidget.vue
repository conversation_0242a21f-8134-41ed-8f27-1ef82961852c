<template>
  <div
    class="recorder-widget"
    :class="{ recording: isRecording, processing: isProcessing }"
    @dblclick="restoreMainWindow"
  >
    <!-- 顶部状态栏 -->
    <div class="top-bar">
      <div class="status-info">
        <div class="recording-indicator" :class="{ active: isRecording }">
          <div class="pulse-ring" v-if="isRecording"></div>
          <div class="status-dot"></div>
        </div>
        <span class="status-text">
          {{ isRecording ? '录音中' : isProcessing ? '处理中' : '就绪' }}
        </span>
      </div>

      <!-- 拖动手柄 -->
      <div class="drag-handle">
        <div class="drag-dots">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 录音时长显示 -->
      <div class="duration-display" v-if="isRecording || recordingDuration > 0">
        <div class="duration-time">{{ formatDuration(recordingDuration) }}</div>
        <div class="duration-label">录音时长</div>
      </div>

      <!-- 波形显示 -->
      <div class="waveform-container" v-if="isRecording">
        <div class="mini-waveform">
          <div
            class="wave-bar"
            v-for="i in 8"
            :key="i"
            :style="{ animationDelay: i * 0.1 + 's' }"
          ></div>
        </div>
      </div>

      <!-- 空闲状态显示 -->
      <div class="idle-display" v-if="!isRecording && recordingDuration === 0 && !isProcessing">
        <div class="idle-icon">🎙️</div>
        <div class="idle-text">点击开始录音</div>
      </div>

      <!-- 处理状态显示 -->
      <div class="processing-display" v-if="isProcessing">
        <div class="processing-spinner"></div>
        <div class="processing-text">处理中...</div>
      </div>
    </div>

    <!-- 底部控制区域 -->
    <div class="control-area">
      <button
        class="record-btn"
        :class="{ recording: isRecording, processing: isProcessing }"
        @click="toggleRecording"
        :disabled="isProcessing"
        :title="isRecording ? '停止录音' : '开始录音'"
      >
        <svg v-if="!isRecording && !isProcessing" viewBox="0 0 24 24" class="btn-icon">
          <circle cx="12" cy="12" r="8" />
        </svg>
        <span v-else-if="isRecording">123123</span>
        <!-- <svg viewBox="0 0 24 24" class="btn-icon">
          <rect x="8" y="8" width="8" height="8" rx="1" />
        </svg>
        <svg v-else viewBox="0 0 24 24" class="btn-icon">
          <path d="M12,4V2A10,10 0 0,0 2,12H4A8,8 0 0,1 12,4Z" />
        </svg> -->
      </button>

      <div class="btn-label">
        {{ isRecording ? '停止' : isProcessing ? '处理' : '录音' }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { useRecorder } from '@/hooks/useRecorder'
import { RecordingState } from '@/constants/app.constants'
import { useRecorderStore } from '@/stores/counter'

// 使用全局状态管理
const recorderStore = useRecorderStore()

// 桌面组件自己的录音器实例
const { audioState, recOpen, recStop } = useRecorder({
  onRecordingComplete: async (blob: Blob, duration: number) => {
    console.log('桌面组件录音完成，准备上传...', duration)
    await uploadAudio(blob)
  },
})

const isRecording = ref(false)
const isProcessing = ref(false)
const recordingDuration = ref(0)
let durationTimer: number | null = null

// 音频处理函数
const uploadAudio = async (audioBlob: Blob) => {
  try {
    isProcessing.value = true

    // 通过Electron API处理音频
    if (window.electronAPI) {
      const result = await window.electronAPI.uploadAudio(audioBlob)
      console.log('音频文件已准备完成:', result)

      if (result.success) {
        // 这里你可以调用你的接口进行上传
        // 可以使用 result.buffer, result.filePath, result.filename 等信息
        console.log('音频文件信息:', {
          filename: result.filename,
          filePath: result.filePath,
          size: result.size,
        })

        // 示例：调用你的上传接口
        // await uploadToYourAPI(result.buffer, result.filename)
      }
    }
  } catch (error) {
    console.error('音频处理失败:', error)
  } finally {
    isProcessing.value = false
  }
}

// 拖动相关状态
const isDragging = ref(false)

// 监听录音状态变化
const updateRecordingState = () => {
  isRecording.value = audioState.value === RecordingState.RECORDING

  if (isRecording.value) {
    startDurationTimer()
  } else {
    stopDurationTimer()
  }

  // 同步到全局状态
  recorderStore.setRecordingState(audioState.value)
  recorderStore.setProcessing(isProcessing.value)
  recorderStore.setRecordingDuration(recordingDuration.value)
}

// 开始录音时长计时
const startDurationTimer = () => {
  recordingDuration.value = 0
  durationTimer = setInterval(() => {
    recordingDuration.value++
    // 同步到全局状态
    recorderStore.setRecordingDuration(recordingDuration.value)
  }, 1000)
}

// 停止录音时长计时
const stopDurationTimer = () => {
  if (durationTimer) {
    clearInterval(durationTimer)
    durationTimer = null
  }
}

// 格式化时长显示
const formatDuration = (seconds: number) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 切换录音状态
const toggleRecording = async () => {
  if (isProcessing.value) return

  try {
    isProcessing.value = true

    if (!isRecording.value) {
      await recOpen()
    } else {
      recStop()
      // 这里可以添加上传逻辑
      setTimeout(() => {
        recordingDuration.value = 0
      }, 2000) // 2秒后重置状态
    }
  } catch (error) {
    console.error('录音操作失败:', error)
  } finally {
    isProcessing.value = false
  }
}

// 拖动功能
const startDrag = (e: MouseEvent) => {
  isDragging.value = true
  const startX = e.clientX
  const startY = e.clientY

  const handleMouseMove = (e: MouseEvent) => {
    const deltaX = e.clientX - startX
    const deltaY = e.clientY - startY

    // 通过IPC通知主进程移动窗口
    if (window.electronAPI) {
      window.electronAPI.moveWindow(deltaX, deltaY)
    }
  }

  const handleMouseUp = () => {
    isDragging.value = false
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

const stopDrag = () => {
  isDragging.value = false
}

// 恢复主窗口
const restoreMainWindow = () => {
  if (window.electronAPI) {
    window.electronAPI.restoreMainWindow()
  }
}

// 监听录音状态变化
onMounted(() => {
  // 使用Vue的watch来监听响应式状态变化
  const stopWatching = watch(
    audioState,
    () => {
      updateRecordingState()
    },
    { immediate: true },
  )

  onUnmounted(() => {
    stopWatching()
    stopDurationTimer()
  })
})
</script>

<style scoped>
.recorder-widget {
  width: 240px;
  height: 180px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  position: relative;
  transition: all 0.3s ease;
  user-select: none;
  -webkit-app-region: drag;
  cursor: move;
  color: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow: hidden;
}

.recorder-widget.recording {
  background: linear-gradient(135deg, #ff3b30 0%, #ff9500 100%);
  box-shadow: 0 8px 32px rgba(255, 59, 48, 0.4);
}

.recorder-widget.processing {
  background: linear-gradient(135deg, #ff9500 0%, #ffcc02 100%);
}

.recorder-widget:hover {
  transform: scale(1.02);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.25);
}

/* 顶部状态栏 */
.top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 16px 16px 0 0;
  -webkit-app-region: drag;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 6px;
}

.recording-indicator {
  position: relative;
  width: 12px;
  height: 12px;
}

.pulse-ring {
  position: absolute;
  top: -2px;
  left: -2px;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

.status-dot {
  width: 12px;
  height: 12px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.recording-indicator.active .status-dot {
  background: #ff3b30;
  animation: blink 1s infinite;
}

.status-text {
  font-size: 14px;
  font-weight: 500;
  opacity: 0.9;
}

.drag-handle {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-app-region: drag;
}

.drag-dots {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.drag-dots span {
  width: 2px;
  height: 2px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12px;
  -webkit-app-region: drag;
}

/* 录音时长显示 */
.duration-display {
  text-align: center;
  margin-bottom: 8px;
}

.duration-time {
  font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
  font-size: 22px;
  font-weight: 600;
  color: #ffeb3b;
  margin-bottom: 4px;
}

.duration-label {
  font-size: 12px;
  opacity: 0.8;
}

/* 波形显示 */
.waveform-container {
  margin: 8px 0;
}

.mini-waveform {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
  height: 20px;
}

.wave-bar {
  width: 3px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 2px;
  animation: wave 1.2s infinite ease-in-out;
}

/* 空闲状态显示 */
.idle-display {
  text-align: center;
  opacity: 0.8;
}

.idle-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

.idle-text {
  font-size: 13px;
  opacity: 0.7;
}

/* 处理状态显示 */
.processing-display {
  text-align: center;
}

.processing-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 4px;
}

.processing-text {
  font-size: 13px;
  opacity: 0.8;
}

/* 底部控制区域 */
.control-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px 12px 14px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 0 0 16px 16px;
  -webkit-app-region: no-drag;
  font-size: 18px;
}

.record-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  margin-bottom: 4px;
  backdrop-filter: blur(10px);
  -webkit-app-region: no-drag;
}

.record-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.record-btn.recording {
  background: rgba(255, 59, 48, 0.3);
  box-shadow: 0 0 20px rgba(255, 59, 48, 0.4);
}

.record-btn.processing {
  background: rgba(255, 149, 0, 0.3);
}

.record-btn.processing .btn-icon {
  animation: spin 1s linear infinite;
}

.record-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-icon {
  width: 20px;
  height: 20px;
  fill: currentColor;
}

.btn-label {
  font-size: 10px;
  font-weight: 500;
  opacity: 0.9;
}

/* 动画 */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
}

@keyframes wave {
  0%,
  100% {
    height: 8px;
  }
  50% {
    height: 16px;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
